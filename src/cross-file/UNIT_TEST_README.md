# 跨文件感知功能

## 功能概述

在单测智能体模式下，`read_file` 工具执行过程中会自动追加跨文件感知逻辑，帮助模型更好地理解代码的上下文。

## 功能特性

1. **自动跨文件感知**: 在单测智能体模式下，读取文件时自动分析该文件中所有import的自定义类文件
2. **类成员属性包含**: 跨文件感知内容类似 `list_code_definition_names` 的实现逻辑，但增加了类成员属性的内容
3. **历史会话缓存**: 最多保留历史会话中最近3次跨文件感知内容，避免context超长问题
4. **智能缓存机制**: 最多20条跨文件信息，避免重复处理

## 输出格式

跨文件感知内容以以下格式追加到 `read_file` 的结果之后：

```
[list_cross_code_definition_names for 'File path here'] Result:
# FileName.java
1--10 | package com.example;
11--20 | public class ExampleClass {
21--25 |     private String field;
26--30 |     public void method() {}
31--31 | }
```

## 使用场景

- **单元测试生成**: 在生成单元测试时，模型可以了解被测试类的依赖关系
- **代码理解**: 帮助模型理解复杂的类继承和依赖关系
- **重构支持**: 在重构代码时提供完整的上下文信息

## 技术实现

### 核心组件

1. **ImportDefinitionsService**: 负责生成跨文件感知内容
   - `generateCrossFileDefinitions()`: 生成跨文件定义内容
   - `generateDefinitionsWithMembers()`: 生成包含成员属性的定义
   - `formatJavaDefinitions()`: 格式化Java类定义

2. **readFileTool**: 集成跨文件感知逻辑
   - 仅在单测智能体模式 (`mode === "test"`) 下生效
   - 自动调用 ImportDefinitionsService 生成跨文件内容

3. **Task类**: 管理历史会话缓存
   - `addCrossFileDefinitionsToHistory()`: 添加到历史缓存
   - `getCrossFileDefinitionsHistory()`: 获取历史内容
   - `cleanupCrossFileDefinitionsHistory()`: 清理重复项

### 缓存策略

- **文件级缓存**: ImportDefinitionsService 内部缓存，最多20条记录
- **历史会话缓存**: Task 级别缓存，最多保留最近3次跨文件感知内容
- **重复项清理**: 自动清理历史记录中的重复内容

## 配置说明

功能自动在单测智能体模式下启用，无需额外配置。

## 注意事项

1. 仅在单测智能体模式下生效
2. 仅处理单个文件的 `read_file` 请求
3. 最多处理20条跨文件信息，避免context过长
4. 历史缓存最多保留3次，平衡context长度和信息完整性
